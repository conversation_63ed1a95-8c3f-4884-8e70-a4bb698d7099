import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/constants.dart';
import '../../../core/utils/validators.dart';
import '../../../core/widgets/auth_widgets.dart';
import '../data/auth_service.dart';

/// Tela de Login completa com layout profissional
class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _cpfController = TextEditingController();
  final _passwordController = TextEditingController();

  bool _isPasswordVisible = false;
  bool _isLoading = false;

  @override
  void dispose() {
    _cpfController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _togglePasswordVisibility() {
    setState(() {
      _isPasswordVisible = !_isPasswordVisible;
    });
  }

  Future<void> _handleLogin() async {
    if (_formKey.currentState?.validate() ?? false) {
      setState(() {
        _isLoading = true;
      });

      try {
        // Realiza o login
        await AuthService.login(_cpfController.text, _passwordController.text);

        // Navega para o dashboard se o login for bem-sucedido
        if (mounted) {
          context.go(AppConstants.dashboardRoute);
        }
      } catch (e) {
        // Exibe mensagem de erro
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(e.toString()),
              backgroundColor: AppTheme.errorColor,
            ),
          );
        }
      } finally {
        // Desativa o loading
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  void _handleCreateAccount() {
    // Navega para a tela de cadastro usando go_router
    context.go(AppConstants.registerRoute);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.navyBlueColor,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 40.0),
          child: Column(
            children: [
              const SizedBox(height: 60),

              // Logo
              _buildLogo(),

              const SizedBox(height: 80),

              // Formulário de login
              _buildLoginForm(),

              const SizedBox(height: 32),

              // Divisor "ou entre com"
              _buildDivider(),

              const SizedBox(height: 24),

              // Botão Google
              _buildGoogleButton(),

              const SizedBox(height: 40),

              // Link criar conta
              _buildCreateAccountLink(),

              const SizedBox(height: 24),
            ],
          ),
        ),
      ),
    );
  }

  /// Constrói a logo do Castelo Forte
  Widget _buildLogo() {
    return Image.asset(
      'assets/images/logo.png',
      width: 300,
      height: 300,
      fit: BoxFit.contain,
    );
  }

  /// Constrói o formulário de login
  Widget _buildLoginForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Campo de CPF
          _buildCpfField(),

          const SizedBox(height: 20),

          // Campo de senha
          _buildPasswordField(),

          const SizedBox(height: 32),

          // Botão de login
          _buildLoginButton(),
        ],
      ),
    );
  }

  /// Constrói o campo de CPF
  Widget _buildCpfField() {
    return TextFormField(
      controller: _cpfController,
      keyboardType: TextInputType.number,
      textInputAction: TextInputAction.next,
      style: const TextStyle(color: AppTheme.snowWhiteColor),
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
        TextInputFormatter.withFunction((oldValue, newValue) {
          final text = newValue.text;
          if (text.length <= 11) {
            String formatted = '';
            for (int i = 0; i < text.length; i++) {
              if (i == 3 || i == 6) {
                formatted += '.';
              } else if (i == 9) {
                formatted += '-';
              }
              formatted += text[i];
            }
            return TextEditingValue(
              text: formatted,
              selection: TextSelection.collapsed(offset: formatted.length),
            );
          }
          return oldValue;
        }),
      ],
      decoration: InputDecoration(
        labelText: 'CPF',
        labelStyle: const TextStyle(color: AppTheme.snowWhiteColor),
        hintText: 'Digite seu CPF',
        hintStyle: TextStyle(
          color: AppTheme.snowWhiteColor.withValues(alpha: 0.7),
        ),
        filled: true,
        fillColor: Colors.transparent,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: AppTheme.snowWhiteColor,
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: AppTheme.snowWhiteColor,
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.goldColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.errorColor, width: 1),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Por favor, digite seu CPF';
        }
        // Remove formatação para validação
        final cpfLimpo = value.replaceAll(RegExp(r'[^0-9]'), '');
        if (cpfLimpo.length != 11) {
          return 'CPF deve ter 11 dígitos';
        }
        if (!_isValidCpf(cpfLimpo)) {
          return 'CPF inválido';
        }
        return null;
      },
    );
  }

  /// Constrói o campo de senha
  Widget _buildPasswordField() {
    return TextFormField(
      controller: _passwordController,
      obscureText: !_isPasswordVisible,
      textInputAction: TextInputAction.done,
      style: const TextStyle(color: AppTheme.snowWhiteColor),
      decoration: InputDecoration(
        labelText: 'Senha',
        labelStyle: const TextStyle(color: AppTheme.snowWhiteColor),
        hintText: 'Digite sua senha',
        hintStyle: TextStyle(
          color: AppTheme.snowWhiteColor.withValues(alpha: 0.7),
        ),
        filled: true,
        fillColor: Colors.transparent,
        suffixIcon: IconButton(
          icon: Icon(
            _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
            color: AppTheme.snowWhiteColor,
          ),
          onPressed: _togglePasswordVisibility,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: AppTheme.snowWhiteColor,
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: AppTheme.snowWhiteColor,
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.goldColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.errorColor, width: 1),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Por favor, digite sua senha';
        }
        if (value.length < AppConstants.minPasswordLength) {
          return 'A senha deve ter pelo menos ${AppConstants.minPasswordLength} caracteres';
        }
        return null;
      },
    );
  }

  /// Constrói o botão de login
  Widget _buildLoginButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _handleLogin,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.goldColor,
          foregroundColor: AppTheme.navyBlueColor,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          elevation: 0,
        ),
        child: _isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppTheme.navyBlueColor,
                  ),
                ),
              )
            : const Text(
                'Entrar',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
      ),
    );
  }

  /// Constrói o divisor "ou entre com"
  Widget _buildDivider() {
    return Row(
      children: [
        const Expanded(
          child: Divider(color: AppTheme.snowWhiteColor, thickness: 1),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            'ou entre com',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.snowWhiteColor,
              fontSize: 14,
            ),
          ),
        ),
        const Expanded(
          child: Divider(color: AppTheme.snowWhiteColor, thickness: 1),
        ),
      ],
    );
  }

  /// Constrói o botão do Google
  Widget _buildGoogleButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: OutlinedButton.icon(
        onPressed: () {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Login com Google será implementado em breve!'),
              backgroundColor: AppTheme.warningColor,
            ),
          );
        },
        style: OutlinedButton.styleFrom(
          side: const BorderSide(color: AppTheme.snowWhiteColor, width: 1),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          backgroundColor: Colors.transparent,
        ),
        icon: const Icon(
          Icons
              .g_mobiledata, // Temporário - será substituído pelo ícone do Google
          color: AppTheme.snowWhiteColor,
          size: 24,
        ),
        label: const Text(
          'Google',
          style: TextStyle(
            color: AppTheme.snowWhiteColor,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  /// Constrói o link "Crie uma conta"
  Widget _buildCreateAccountLink() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          'Não tem uma conta? ',
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(color: AppTheme.snowWhiteColor),
        ),
        TextButton(
          onPressed: _handleCreateAccount,
          style: TextButton.styleFrom(
            padding: EdgeInsets.zero,
            minimumSize: Size.zero,
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
          child: const Text(
            'Crie uma conta',
            style: TextStyle(
              color: AppTheme.goldColor,
              fontWeight: FontWeight.bold,
              decoration: TextDecoration.underline,
              decorationColor: AppTheme.goldColor,
            ),
          ),
        ),
      ],
    );
  }

  /// Valida CPF usando o algoritmo oficial
  bool _isValidCpf(String cpf) {
    if (cpf.length != 11) return false;

    // Verifica se todos os dígitos são iguais
    if (RegExp(r'^(\d)\1*$').hasMatch(cpf)) return false;

    // Calcula o primeiro dígito verificador
    int sum = 0;
    for (int i = 0; i < 9; i++) {
      sum += int.parse(cpf[i]) * (10 - i);
    }
    int firstDigit = 11 - (sum % 11);
    if (firstDigit >= 10) firstDigit = 0;

    // Verifica o primeiro dígito
    if (int.parse(cpf[9]) != firstDigit) return false;

    // Calcula o segundo dígito verificador
    sum = 0;
    for (int i = 0; i < 10; i++) {
      sum += int.parse(cpf[i]) * (11 - i);
    }
    int secondDigit = 11 - (sum % 11);
    if (secondDigit >= 10) secondDigit = 0;

    // Verifica o segundo dígito
    return int.parse(cpf[10]) == secondDigit;
  }
}
