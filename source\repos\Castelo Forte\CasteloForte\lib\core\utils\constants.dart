/// Constantes da aplicação
class AppConstants {
  // Rotas
  static const String splashRoute = '/';
  static const String loginRoute = '/login';
  static const String registerRoute = '/register';
  static const String passwordReentryRoute = '/password-reentry';
  static const String dashboardRoute = '/dashboard';
  static const String profileRoute = '/profile';
  static const String editProfileRoute = '/profile/edit';
  static const String settingsRoute = '/settings';
  static const String securityRoute = '/security';
  static const String transactionsRoute = '/transactions';
  static const String reportsRoute = '/reports';
  static const String goalsRoute = '/goals';
  static const String categoriesRoute = '/categories';
  static const String accountsRoute = '/accounts';
  static const String accountFormRoute = '/accounts/form';
  static const String cardsRoute = '/cards';
  static const String cardFormRoute = '/cards/form';
  static const String helpRoute = '/help';
  static const String notificationsRoute = '/notifications';
  static const String apiSettingsRoute = '/settings/api';
  static const String debugRoute = '/debug';

  // Outras constantes
  static const String appName = 'Castelo Forte';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'Gestão financeira pessoal';

  // Configurações de API
  // static const String defaultApiBaseUrlClient = 'https://localhost:7097';
  // static const String defaultApiBaseUrlClient = 'https://localhost:7184';
  static const String defaultApiBaseUrlClient = 'https://localhost:7148';
  static const String defaultApiBaseUrlAdmin = 'https://localhost:7148';

  // Configurações de segurança
  static const int minPasswordLength = 8;

  // Configurações de animação
  static const Duration defaultAnimationDuration = Duration(milliseconds: 800);
  static const Duration splashMinDuration = Duration(seconds: 2);
}
