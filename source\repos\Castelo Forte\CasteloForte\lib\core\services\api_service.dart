import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/constants.dart';
import 'logger_service.dart';

/// Serviço para comunicação com a API
class ApiService {
  static const String _apiBaseUrlKey = 'api_base_url';
  static String _baseUrl = AppConstants.defaultApiBaseUrlClient;
  static String _baseUrlAdmin = AppConstants.defaultApiBaseUrlAdmin;
  static String? _authToken;

  /// Inicializa o serviço de API
  static Future<void> init() async {
    final prefs = await SharedPreferences.getInstance();
    _baseUrl =
        prefs.getString(_apiBaseUrlKey) ?? AppConstants.defaultApiBaseUrlClient;

    // Configura para ignorar certificados SSL em desenvolvimento
    HttpOverrides.global = _DevHttpOverrides();
  }

  /// Define a URL base da API
  static Future<void> setBaseUrl(String url) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_apiBaseUrlKey, url);
    _baseUrl = url;
  }

  /// Obtém a URL base da API
  static String getBaseUrl() {
    return _baseUrl;
  }

  /// Define o token de autenticação
  static void setAuthToken(String token) {
    _authToken = token;
  }

  /// Limpa o token de autenticação
  static void clearAuthToken() {
    _authToken = null;
  }

  /// Realiza uma requisição GET
  static Future<Map<String, dynamic>> get(
    String endpoint, {
    Map<String, String>? queryParams,
  }) async {
    final url = _buildUrl(endpoint, queryParams: queryParams);
    final response = await http.get(Uri.parse(url), headers: _getHeaders());

    return _handleResponse(response);
  }

  /// Realiza uma requisição POST
  static Future<Map<String, dynamic>> post(
    String endpoint,
    Map<String, dynamic> data,
  ) async {
    final url = _buildUrl(endpoint);
    final headers = _getHeaders();
    final body = json.encode(data);

    LoggerService.debug('POST Request:');
    LoggerService.debug('URL: $url');
    LoggerService.debug('Headers: $headers');
    LoggerService.debug('Body: $body');

    final response = await http.post(
      Uri.parse(url),
      headers: headers,
      body: body,
    );

    LoggerService.debug('Response Status: ${response.statusCode}');
    LoggerService.debug('Response Body: ${response.body}');

    return _handleResponse(response);
  }

  /// Realiza uma requisição PUT
  static Future<Map<String, dynamic>> put(
    String endpoint,
    Map<String, dynamic> data,
  ) async {
    final url = _buildUrl(endpoint);
    final response = await http.put(
      Uri.parse(url),
      headers: _getHeaders(),
      body: json.encode(data),
    );

    return _handleResponse(response);
  }

  /// Realiza uma requisição DELETE
  static Future<Map<String, dynamic>> delete(String endpoint) async {
    final url = _buildUrl(endpoint);
    final response = await http.delete(Uri.parse(url), headers: _getHeaders());

    return _handleResponse(response);
  }

  /// Testa a conectividade com a API
  static Future<bool> testConnection() async {
    try {
      // Testa usando o endpoint do Swagger que sabemos que existe
      final url = '$_baseUrl/swagger/index.html';
      LoggerService.connectivity('Testando conectividade: $url');

      final response = await http
          .get(
            Uri.parse(url),
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
          )
          .timeout(const Duration(seconds: 5));

      LoggerService.connectivity('Status Code: ${response.statusCode}');
      return response.statusCode == 200;
    } catch (e) {
      LoggerService.failure('Erro na conectividade: $e');
      return false;
    }
  }

  /// Realiza login na API (endpoint do cliente)
  static Future<Map<String, dynamic>> login(Map<String, dynamic> data) async {
    try {
      final url = _buildUrl('/api/Auth/login');
      LoggerService.api('Fazendo requisição para: $url');
      LoggerService.api('Dados enviados: ${json.encode(data)}');

      final response = await http
          .post(
            Uri.parse(url),
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            body: json.encode(data),
          )
          .timeout(const Duration(seconds: 10));

      LoggerService.api('Status Code: ${response.statusCode}');
      LoggerService.api('Response Body: ${response.body}');

      if (response.statusCode == 200) {
        if (response.body.isEmpty) {
          throw Exception('Resposta vazia do servidor');
        }
        return json.decode(response.body);
      } else if (response.statusCode == 404) {
        throw Exception('Usuário ou senha incorretos');
      } else if (response.statusCode == 401) {
        throw Exception('Usuário desativado ou credenciais inválidas');
      } else {
        throw Exception('Erro do servidor: ${response.statusCode}');
      }
    } catch (e) {
      LoggerService.failure('Erro na requisição: $e');
      rethrow;
    }
  }

  /// Realiza registro de usuário na API (endpoint admin)
  static Future<Map<String, dynamic>> register(
    Map<String, dynamic> data,
  ) async {
    try {
      final url = _buildUrlAdmin('/api/UsuarioPublico/cadastrar');
      LoggerService.api('Fazendo requisição de registro para: $url');
      LoggerService.api('Dados enviados: ${json.encode(data)}');

      final response = await http
          .post(
            Uri.parse(url),
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            body: json.encode(data),
          )
          .timeout(const Duration(seconds: 10));

      LoggerService.api('Status Code: ${response.statusCode}');
      LoggerService.api('Response Body: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.body.isEmpty) {
          throw Exception('Resposta vazia do servidor');
        }
        return json.decode(response.body);
      } else if (response.statusCode == 400) {
        throw Exception('Dados inválidos para registro');
      } else if (response.statusCode == 409) {
        throw Exception('Usuário já existe');
      } else {
        throw Exception('Erro do servidor: ${response.statusCode}');
      }
    } catch (e) {
      LoggerService.failure('Erro na requisição de registro: $e');
      rethrow;
    }
  }

  /// Constrói a URL completa para o endpoint
  static String _buildUrl(String endpoint, {Map<String, String>? queryParams}) {
    String finalUrl;

    // Se o endpoint já começa com /api, usa como está
    if (endpoint.startsWith('/api/')) {
      finalUrl = '$_baseUrl$endpoint';
    }
    // Se o endpoint já começa com /, adiciona apenas a base
    else if (endpoint.startsWith('/')) {
      finalUrl = '$_baseUrl$endpoint';
    }
    // Se é uma URL completa (http/https), usa como está
    else if (endpoint.startsWith('http://') ||
        endpoint.startsWith('https://')) {
      finalUrl = endpoint;
    }
    // Para endpoints relativos, adiciona /api/ automaticamente
    else {
      finalUrl = '$_baseUrl/api/$endpoint';
    }

    // Adiciona query parameters se fornecidos
    if (queryParams != null && queryParams.isNotEmpty) {
      final uri = Uri.parse(finalUrl);
      final newUri = uri.replace(
        queryParameters: {...uri.queryParameters, ...queryParams},
      );
      finalUrl = newUri.toString();
    }

    LoggerService.api(
      'URL construída: $finalUrl (endpoint original: $endpoint)',
    );
    return finalUrl;
  }

  static String _buildUrlAdmin(
    String endpoint, {
    Map<String, String>? queryParams,
  }) {
    String finalUrl;

    // Se o endpoint já começa com /api, usa como está
    if (endpoint.startsWith('/api/')) {
      finalUrl = '$_baseUrlAdmin$endpoint';
    }
    // Se o endpoint já começa com /, adiciona apenas a base
    else if (endpoint.startsWith('/')) {
      finalUrl = '$_baseUrlAdmin$endpoint';
    }
    // Se é uma URL completa (http/https), usa como está
    else if (endpoint.startsWith('http://') ||
        endpoint.startsWith('https://')) {
      finalUrl = endpoint;
    }
    // Para endpoints relativos, adiciona /api/ automaticamente
    else {
      finalUrl = '$_baseUrlAdmin/api/$endpoint';
    }

    // Adiciona query parameters se fornecidos
    if (queryParams != null && queryParams.isNotEmpty) {
      final uri = Uri.parse(finalUrl);
      final newUri = uri.replace(
        queryParameters: {...uri.queryParameters, ...queryParams},
      );
      finalUrl = newUri.toString();
    }

    LoggerService.api(
      'URL construída: $finalUrl (endpoint original: $endpoint)',
    );
    return finalUrl;
  }

  /// Obtém os headers para as requisições
  static Map<String, String> _getHeaders() {
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (_authToken != null) {
      headers['Authorization'] = 'Bearer $_authToken';
    }

    return headers;
  }

  /// Trata a resposta da API
  static Map<String, dynamic> _handleResponse(http.Response response) {
    if (response.statusCode >= 200 && response.statusCode < 300) {
      if (response.body.isEmpty) {
        return {};
      }
      return json.decode(response.body);
    } else {
      throw ApiException(
        statusCode: response.statusCode,
        message: _getErrorMessage(response),
      );
    }
  }

  /// Obtém a mensagem de erro da resposta
  static String _getErrorMessage(http.Response response) {
    try {
      final Map<String, dynamic> body = json.decode(response.body);
      return body['message'] ?? 'Erro desconhecido';
    } catch (e) {
      return 'Erro na comunicação com o servidor';
    }
  }

  /// Método de debug para testar construção de URLs
  static void debugUrls() {
    final testEndpoints = [
      'Categoria',
      'Cartao',
      'Conta',
      'Dashboard/dados',
      'Meta',
      '/api/Auth/login',
      'https://example.com/test',
    ];

    LoggerService.info('=== DEBUG: Testando construção de URLs ===');
    for (final endpoint in testEndpoints) {
      final url = _buildUrl(endpoint);
      LoggerService.info('Endpoint: "$endpoint" -> URL: "$url"');
    }
    LoggerService.info('=== FIM DEBUG ===');
  }
}

/// Exceção lançada pelo ApiService
class ApiException implements Exception {
  final int statusCode;
  final String message;

  ApiException({required this.statusCode, required this.message});

  @override
  String toString() {
    return 'ApiException: [$statusCode] $message';
  }
}

/// Classe para ignorar certificados SSL em desenvolvimento
class _DevHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback = (X509Certificate cert, String host, int port) {
        // Em produção, você deve validar o certificado adequadamente
        // Para desenvolvimento com localhost, ignoramos a validação
        return true;
      };
  }
}
