/// Modelo de dados para cartão de crédito completo
class CardModel {
  final String id;
  final String nomeCartao;
  final String numeroCartao; // Número completo do cartão
  final String ultimosDigitos;
  final String bandeira;
  final bool ativo;
  final String? apelido;
  final double limite;
  final DateTime? dataVencimento;
  final DateTime dataCriacao;
  final DateTime? dataUltimaAtualizacao;

  const CardModel({
    required this.id,
    required this.nomeCartao,
    required this.numeroCartao,
    required this.ultimosDigitos,
    required this.bandeira,
    required this.ativo,
    this.apelido,
    required this.limite,
    this.dataVencimento,
    required this.dataCriacao,
    this.dataUltimaAtualizacao,
  });

  /// Cria uma instância a partir de JSON
  factory CardModel.fromJson(Map<String, dynamic> json) {
    // Extrai os últimos 4 dígitos do número do cartão
    final numeroCartao = json['numeroCartao']?.toString() ?? '';
    final ultimosDigitos = numeroCartao.length >= 4
        ? numeroCartao.substring(numeroCartao.length - 4)
        : numeroCartao;

    // Determina a bandeira baseada no número do cartão
    String bandeira = 'Desconhecida';
    if (numeroCartao.isNotEmpty) {
      final firstDigit = numeroCartao[0];
      switch (firstDigit) {
        case '4':
          bandeira = 'Visa';
          break;
        case '5':
          bandeira = 'Mastercard';
          break;
        case '3':
          bandeira = 'American Express';
          break;
        case '6':
          bandeira = 'Discover';
          break;
        default:
          bandeira = 'Outros';
      }
    }

    return CardModel(
      id: json['id']?.toString() ?? '',
      nomeCartao: json['nomeCartao']?.toString() ?? '',
      numeroCartao: numeroCartao,
      ultimosDigitos: ultimosDigitos,
      bandeira: bandeira,
      ativo: json['flgAtivo'] as bool? ?? true, // API usa 'flgAtivo'
      apelido: json['apelidoCartao']?.toString(), // API usa 'apelidoCartao'
      limite: (json['limite'] as num?)?.toDouble() ?? 0.0,
      dataVencimento:
          json['dtaValidade'] !=
              null // API usa 'dtaValidade'
          ? DateTime.parse(json['dtaValidade'].toString())
          : null,
      dataCriacao:
          json['dtaCadastro'] !=
              null // API usa 'dtaCadastro'
          ? DateTime.parse(json['dtaCadastro'].toString())
          : DateTime.now(),
      dataUltimaAtualizacao: json['dataUltimaAtualizacao'] != null
          ? DateTime.parse(json['dataUltimaAtualizacao'].toString())
          : null,
    );
  }

  /// Converte para JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nomeCartao': nomeCartao,
      'numeroCartao': numeroCartao,
      'ultimosDigitos': ultimosDigitos,
      'bandeira': bandeira,
      'ativo': ativo,
      'apelido': apelido,
      'limite': limite,
      'dataVencimento': dataVencimento?.toIso8601String(),
      'dataCriacao': dataCriacao.toIso8601String(),
      'dataUltimaAtualizacao': dataUltimaAtualizacao?.toIso8601String(),
    };
  }

  /// Retorna o nome de exibição (apelido ou nome do cartão)
  String get nomeExibicao =>
      apelido?.isNotEmpty == true ? apelido! : nomeCartao;

  /// Retorna o número mascarado do cartão
  String get numeroMascarado => '**** **** **** $ultimosDigitos';

  /// Verifica se é VISA
  bool get isVisa => bandeira.toUpperCase() == 'VISA';

  /// Verifica se é Mastercard
  bool get isMastercard =>
      bandeira.toUpperCase() == 'MASTER' ||
      bandeira.toUpperCase() == 'MASTERCARD';

  /// Verifica se o cartão está vencido
  bool get isVencido {
    if (dataVencimento == null) return false;
    return DateTime.now().isAfter(dataVencimento!);
  }

  /// Verifica se o cartão vence em breve (próximos 30 dias)
  bool get venceEmBreve {
    if (dataVencimento == null) return false;
    final agora = DateTime.now();
    final em30Dias = agora.add(const Duration(days: 30));
    return dataVencimento!.isAfter(agora) && dataVencimento!.isBefore(em30Dias);
  }

  /// Retorna a data de vencimento formatada
  String get dataVencimentoFormatada {
    if (dataVencimento == null) return 'Não informado';
    return '${dataVencimento!.month.toString().padLeft(2, '0')}/${dataVencimento!.year}';
  }

  /// Cria uma cópia com campos modificados
  CardModel copyWith({
    String? id,
    String? nomeCartao,
    String? numeroCartao,
    String? ultimosDigitos,
    String? bandeira,
    bool? ativo,
    String? apelido,
    double? limite,
    DateTime? dataVencimento,
    DateTime? dataCriacao,
    DateTime? dataUltimaAtualizacao,
  }) {
    return CardModel(
      id: id ?? this.id,
      nomeCartao: nomeCartao ?? this.nomeCartao,
      numeroCartao: numeroCartao ?? this.numeroCartao,
      ultimosDigitos: ultimosDigitos ?? this.ultimosDigitos,
      bandeira: bandeira ?? this.bandeira,
      ativo: ativo ?? this.ativo,
      apelido: apelido ?? this.apelido,
      limite: limite ?? this.limite,
      dataVencimento: dataVencimento ?? this.dataVencimento,
      dataCriacao: dataCriacao ?? this.dataCriacao,
      dataUltimaAtualizacao:
          dataUltimaAtualizacao ?? this.dataUltimaAtualizacao,
    );
  }

  @override
  String toString() {
    return 'CardModel(id: $id, nomeCartao: $nomeCartao, ultimosDigitos: $ultimosDigitos, bandeira: $bandeira, ativo: $ativo, apelido: $apelido)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CardModel &&
        other.id == id &&
        other.nomeCartao == nomeCartao &&
        other.ultimosDigitos == ultimosDigitos &&
        other.bandeira == bandeira &&
        other.ativo == ativo &&
        other.apelido == apelido &&
        other.dataVencimento == dataVencimento &&
        other.dataCriacao == dataCriacao &&
        other.dataUltimaAtualizacao == dataUltimaAtualizacao;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      nomeCartao,
      ultimosDigitos,
      bandeira,
      ativo,
      apelido,
      dataVencimento,
      dataCriacao,
      dataUltimaAtualizacao,
    );
  }
}

/// Modelo para criação/edição de cartão baseado na CartaoViewModel da API
class CardFormModel {
  final String? id;
  final String numeroCartao;
  final String nomeCartao;
  final DateTime dtaValidade;
  final String cvv;
  final int diaFechamento;
  final int diaVencimento;
  final String apelidoCartao;
  final bool? flgAtivo;
  final double? limiteCartao;

  const CardFormModel({
    this.id,
    required this.numeroCartao,
    required this.nomeCartao,
    required this.dtaValidade,
    required this.cvv,
    required this.diaFechamento,
    required this.diaVencimento,
    required this.apelidoCartao,
    this.flgAtivo,
    this.limiteCartao,
  });

  /// Cria uma instância a partir de JSON
  factory CardFormModel.fromJson(Map<String, dynamic> json) {
    return CardFormModel(
      id: json['id']?.toString(),
      numeroCartao: json['numeroCartao']?.toString() ?? '',
      nomeCartao: json['nomeCartao']?.toString() ?? '',
      dtaValidade: json['dtaValidade'] != null
          ? DateTime.parse(json['dtaValidade'].toString())
          : DateTime.now(),
      cvv: json['cvv']?.toString() ?? '',
      diaFechamento: json['diaFechamento'] as int? ?? 15,
      diaVencimento: json['diaVencimento'] as int? ?? 10,
      apelidoCartao: json['apelidoCartao']?.toString() ?? '',
      flgAtivo: json['flgAtivo'] as bool?,
      limiteCartao: json['limiteCartao'] as double?,
    );
  }

  /// Converte para JSON para envio à API (CartaoViewModel)
  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      'numeroCartao': numeroCartao,
      'nomeCartao': nomeCartao,
      'dtaValidade': dtaValidade.toIso8601String(),
      'cvv': cvv,
      'diaFechamento': diaFechamento,
      'diaVencimento': diaVencimento,
      'apelidoCartao': apelidoCartao,
      if (flgAtivo != null) 'flgAtivo': flgAtivo,
      if (limiteCartao != null) 'limiteCartao': limiteCartao,
    };
  }

  /// Cria uma cópia com campos modificados
  CardFormModel copyWith({
    String? id,
    String? numeroCartao,
    String? nomeCartao,
    DateTime? dtaValidade,
    String? cvv,
    int? diaFechamento,
    int? diaVencimento,
    String? apelidoCartao,
    bool? flgAtivo,
    double? limiteCartao,
  }) {
    return CardFormModel(
      id: id ?? this.id,
      numeroCartao: numeroCartao ?? this.numeroCartao,
      nomeCartao: nomeCartao ?? this.nomeCartao,
      dtaValidade: dtaValidade ?? this.dtaValidade,
      cvv: cvv ?? this.cvv,
      diaFechamento: diaFechamento ?? this.diaFechamento,
      diaVencimento: diaVencimento ?? this.diaVencimento,
      apelidoCartao: apelidoCartao ?? this.apelidoCartao,
      flgAtivo: flgAtivo ?? this.flgAtivo,
      limiteCartao: limiteCartao ?? this.limiteCartao,
    );
  }

  /// Obtém os últimos 4 dígitos do número do cartão
  String get ultimosDigitos {
    if (numeroCartao.length >= 4) {
      return numeroCartao.substring(numeroCartao.length - 4);
    }
    return numeroCartao;
  }

  @override
  String toString() {
    return 'CardFormModel(id: $id, nomeCartao: $nomeCartao, numeroCartao: $numeroCartao, apelidoCartao: $apelidoCartao)';
  }

  /// Cria uma instância a partir de CardModel para edição
  factory CardFormModel.fromCard(CardModel card) {
    return CardFormModel(
      id: card.id,
      numeroCartao: card.numeroCartao, // Usa o número completo do cartão
      nomeCartao: card.nomeCartao,
      dtaValidade:
          card.dataVencimento ?? DateTime.now().add(const Duration(days: 365)),
      cvv: '', // CVV não é armazenado por segurança
      diaFechamento: 15, // Valor padrão
      diaVencimento: 10, // Valor padrão
      apelidoCartao: card.apelido ?? '',
      flgAtivo: card.ativo,
      limiteCartao: card.limite,
    );
  }
}
